package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.DomainEntity;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

/** Entity class representing get fare count information for machine learning. */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MlGetFareRequestAggStatsEntity implements Serializable, DomainEntity<Long> {

  @Serial private static final long serialVersionUID = 1L;

  private Long id;
  private Instant startTimestamp;
  private Instant endTimestamp;
  private String areaType;
  private Long pickupRegionId;
  private String regionVersion;
  private Long modelId;
  private Integer getFareCount;
  private Instant createTimestamp;
}
