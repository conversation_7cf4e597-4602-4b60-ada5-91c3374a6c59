package integration.tests.surgecomputation.machinelearningdataaggregation;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.cdg.pmg.ngp.me.dynamicpricing.constants.RequestCountConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.GetFareCountEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.SurgeAreaTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter.RequestCounterServiceAdapter;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.MlGetFareRequestAggStatsJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.MlGetFareRequestAggStatsJPARepository;
import integration.IntegrationTestBase;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.LongStream;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class AggregateGetFareCountEveryMinuteIT extends IntegrationTestBase {

  private final RequestCounterServiceAdapter requestCounterService;
  private final MlGetFareRequestAggStatsJPARepository mlGetFareRequestAggStatsJPARepository;

  @Test
  void shouldAggregateGetFareCountEveryMinuteSuccess() throws Exception {
    final Instant now = Instant.now();
    final Instant endTime = now.truncatedTo(ChronoUnit.MINUTES);
    final Instant startTime = endTime.minus(1, ChronoUnit.MINUTES);

    // region based
    LongStream.rangeClosed(1, 5)
        .forEach(
            index -> {
              GetFareCountEntity getFareCountEntity =
                  new GetFareCountEntity(
                      SurgeAreaTypeEnum.REGION, index, "0.0.1", index, startTime.plusSeconds(1L));

              requestCounterService.recordEndpointRequest(
                  RequestCountConstant.MULTI_FARE, getFareCountEntity);
            });

    // zone based
    LongStream.rangeClosed(1, 5)
        .forEach(
            index -> {
              GetFareCountEntity getFareCountEntity =
                  new GetFareCountEntity(
                      SurgeAreaTypeEnum.ZONE, index, "0.0.1", null, startTime.plusSeconds(1L));

              requestCounterService.recordEndpointRequest(
                  RequestCountConstant.MULTI_FARE, getFareCountEntity);
            });

    // Hear manually trigger sync to Redis, instead of scheduled task.
    requestCounterService.syncLocalDataToRedis();

    dynamicPricingServiceApi.aggregateGetFareCountEveryMinute().execute();

    List<MlGetFareRequestAggStatsJPA> list = mlGetFareRequestAggStatsJPARepository.findAll();
    assertEquals(1, list.size());
  }
}
