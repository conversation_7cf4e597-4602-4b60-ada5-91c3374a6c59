---
openapi: 3.0.1
info:
  title: Mobility Engine API
  description: Mobility Engine API
  version: '1.0.0'
servers:
  - url: http://localhost:8080
    description: Generated server url
paths:
  "/":
    get:
      tags:
        - home-controller
      summary: Home API
      operationId: getResponse
      responses:
        '200':
          description: Application is running!

  ############################ Config Management ############################
  ## Get Location Surcharge by Condition
  /v1.0/config/location-surcharge:
    post:
      tags:
        - Config Management
      summary: Get location surcharge by condition
      operationId: getLocationSurchargeConfig
      requestBody:
        description: A JSON of config object to get location surcharge config
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPickupLocationSurchargeRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPickupLocationSurchargeResponse'

        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'

        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundResponse'

        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  ## Load Dynamic Pricing Configs to Cache
  /v1.0/load-configs:
    post:
      tags:
        - Config Management
      summary: Load dynamic pricing configs to cache
      operationId: loadConfigs
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoadConfigsSuccessResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  ## Retrieve Current Active Dynamic Surge in Cache
  /v1.0/configs/cache/dynamic-surge:
    get:
      tags:
        - Config Management
      summary: Retrieve current dynamic surge in cache
      operationId: getDynamicSurgeFromCache
      responses:
        '200':
          description: Response message do task successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DynamicSurgeResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                required:
                  - error
                  - traceId
                  - path
                  - timestamp
                type: object
                properties:
                  timestamp:
                    type: string
                  traceId:
                    type: string
                  path:
                    type: string
                  error:
                    required:
                      - code
                      - message
                    type: object
                    properties:
                      code:
                        type: string
                      message:
                        type: string
                example: {
                  "traceId": "00000000000000000000000000000000",
                  "error": {
                    "message": "Internal Service Error",
                    "code": "InternalServiceError"
                  },
                  "path": "/v1.0/pricing/book-a-ride/scheduler",
                  "timestamp": "2023-07-13T04:05:34.310625200Z"
                }
  ## Retrieve R1 Dynamic Surge in Cache
  /v1.0/configs/cache/dynamic-surge-r1:
    get:
      tags:
        - Config Management
      summary: Retrieve current dynamic surge in cache
      operationId: getR1DynamicSurgeFromCache
      responses:
        '200':
          description: Response message do task successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DynamicSurgeResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                required:
                  - error
                  - traceId
                  - path
                  - timestamp
                type: object
                properties:
                  timestamp:
                    type: string
                  traceId:
                    type: string
                  path:
                    type: string
                  error:
                    required:
                      - code
                      - message
                    type: object
                    properties:
                      code:
                        type: string
                      message:
                        type: string
                example: {
                  "traceId": "00000000000000000000000000000000",
                  "error": {
                    "message": "Internal Service Error",
                    "code": "InternalServiceError"
                  },
                  "path": "/v1.0/pricing/book-a-ride/scheduler",
                  "timestamp": "2023-07-13T04:05:34.310625200Z"
                }
  ## Retrieve R2 Dynamic Surge in Cache
  /v1.0/configs/cache/dynamic-surge-r2:
    get:
      tags:
        - Config Management
      summary: Retrieve current dynamic surge in cache
      operationId: getR2DynamicSurgeFromCache
      responses:
        '200':
          description: Response message do task successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DynamicSurgeResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                required:
                  - error
                  - traceId
                  - path
                  - timestamp
                type: object
                properties:
                  timestamp:
                    type: string
                  traceId:
                    type: string
                  path:
                    type: string
                  error:
                    required:
                      - code
                      - message
                    type: object
                    properties:
                      code:
                        type: string
                      message:
                        type: string
                example: {
                  "traceId": "00000000000000000000000000000000",
                  "error": {
                    "message": "Internal Service Error",
                    "code": "InternalServiceError"
                  },
                  "path": "/v1.0/pricing/book-a-ride/scheduler",
                  "timestamp": "2023-07-13T04:05:34.310625200Z"
                }
  ## Update CBD Addresses
  /v1.0/config/cbd-surcharge:
    post:
      tags:
        - Config Management
      summary: Update CBD address
      operationId: updateCbdAddress
      requestBody:
        description: A JSON of config object to update CBD address
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCBDAddressRequest'
      responses:
        '204':
          description: Successful operation
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  ## Reload CDG Addresses
  /v1.0/config/loc-surcharge/reload:
    post:
      tags:
        - Config Management
      summary: Reload location surcharge config Cache
      operationId: reloadCbdConfigCache
      requestBody:
        description: A JSON of list addressRef need to reload
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LocReloadCacheRequest'
      responses:
        '204':
          description: Successful operation
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  ############################ S2 Cell Rest API Inbound Adapter ############################
  ## Fetch all S2 Cells
  /v1.0/pricing/s2cell/fetch:
    get:
      tags:
        - S2 Cell Rest API Inbound Adapter
      summary: Fetch S2 Cache API
      operationId: fetchCache
      responses:
        '204':
          description: Successful operation
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                required:
                  - error
                  - traceId
                  - path
                  - timestamp
                type: object
                properties:
                  timestamp:
                    type: string
                  traceId:
                    type: string
                  path:
                    type: string
                  error:
                    required:
                      - code
                      - message
                    type: object
                    properties:
                      code:
                        type: string
                      message:
                        type: string
                example: {
                  "traceId": "00000000000000000000000000000000",
                  "error": {
                    "message": "S2Cell can not be fetch",
                    "code": "BadRequestException"
                  },
                  "path": "/v1.0/pricing/s2cell/fetch",
                  "timestamp": "2023-07-13T04:01:56.956278600Z"
                }
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                required:
                  - error
                  - traceId
                  - path
                  - timestamp
                type: object
                properties:
                  timestamp:
                    type: string
                  traceId:
                    type: string
                  path:
                    type: string
                  error:
                    required:
                      - code
                      - message
                    type: object
                    properties:
                      code:
                        type: string
                      message:
                        type: string
                example: {
                  "traceId": "00000000000000000000000000000000",
                  "error": {
                    "message": "Internal Service Error",
                    "code": "InternalServiceError"
                  },
                  "path": "/v1.0/pricing/s2cell/fetch",
                  "timestamp": "2023-07-13T04:05:34.310625200Z"
                }

  ############################ Book-A-Ride Controller ############################
  ## Init Scheduler Task
  /v1.0/pricing/book-a-ride/scheduler:
    get:
      tags:
        - book-a-ride-controller
      summary: Init scheduler task.
      operationId: uploadFileScheduler
      responses:
        '200':
          description: Response message do task successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookARideResponse'
        '204':
          description: Success but no content
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                required:
                  - error
                  - traceId
                  - path
                  - timestamp
                type: object
                properties:
                  timestamp:
                    type: string
                  traceId:
                    type: string
                  path:
                    type: string
                  error:
                    required:
                      - code
                      - message
                    type: object
                    properties:
                      code:
                        type: string
                      message:
                        type: string
                example: {
                  "traceId": "00000000000000000000000000000000",
                  "error": {
                    "message": "Internal Service Error",
                    "code": "InternalServiceError"
                  },
                  "path": "/v1.0/pricing/book-a-ride/scheduler",
                  "timestamp": "2023-07-13T04:05:34.310625200Z"
                }

  ############################ Parameter Config Controller ############################
  ## Get param config by listFareType
  ## Insert/Update fare type config
  /v1.0/pricing/param-config/fare-type-config:
    get:
      tags:
        - Parameter Config Controller
      summary: Get param config by listFareType
      operationId: getParamConfigsByListFareType
      parameters:
        - in: query
          name: listFareType
          schema:
            type: array
            items:
              type: string
            example:
              - DYNP_BOOKING_FEE
              - DYNP_DESURGE_MAX_CAP
              - DYNP_DURATION_RATE
              - DYNP_FLAG_DOWN_RATE
              - DYNP_MAX_CAP
              - DYNP_MIN_CAP
              - DYNP_MIN_SURGE_AMOUNT
              - DYNP_PEAK_MIDNIGHT_HOUR_RATE
              - DYNP_SURGE_BUFFER
              - DYNP_TIER_1_END_DIST
              - DYNP_TIER_1_PRICE_MULTIPLIER
              - DYNP_TIER_1_START_DIST
              - DYNP_TIER_2_END_DIST
              - DYNP_TIER_2_PRICE_MULTIPLIER
              - DYNP_TIER_2_START_DIST
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FareTypeConfigsResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - Parameter Config Controller
      summary: Insert/Update fare type config
      operationId: insertOrUpdateFareTypeConfig
      requestBody:
        description: A JSON of config object to insert/update fare type config
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FareTypeConfigRequest'
      responses:
        '200':
          description: Response message update successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateSuccessResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                required:
                  - error
                  - traceId
                  - path
                  - timestamp
                type: object
                properties:
                  timestamp:
                    type: string
                  traceId:
                    type: string
                  path:
                    type: string
                  error:
                    required:
                      - code
                      - message
                    type: object
                    properties:
                      code:
                        type: string
                      message:
                        type: string
                example: {
                  "traceId": "00000000000000000000000000000000",
                  "error": {
                    "message": "Internal Service Error",
                    "code": "InternalServiceError"
                  },
                  "path": "/v1.0/pricing/parameter-config",
                  "timestamp": "2023-07-13T04:05:34.310625200Z"
                }
  ## Get pricing range configs
  ## Insert/Update pricing range config
  /v1.0/pricing/param-config/pricing-range-config:
    get:
      tags:
        - Parameter Config Controller
      summary: Get pricing range configs
      operationId: getPricingRangeConfigs
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PricingRangeConfigsResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                required:
                  - error
                  - traceId
                  - path
                  - timestamp
                type: object
                properties:
                  timestamp:
                    type: string
                  traceId:
                    type: string
                  path:
                    type: string
                  error:
                    required:
                      - code
                      - message
                    type: object
                    properties:
                      code:
                        type: string
                      message:
                        type: string
                example: {
                  "traceId": "00000000000000000000000000000000",
                  "error": {
                    "message": "Internal Service Error",
                    "code": "InternalServiceError"
                  },
                  "path": "/v1.0/pricing/parameter-config",
                  "timestamp": "2023-07-13T04:05:34.310625200Z"
                }
    post:
      tags:
        - Parameter Config Controller
      summary: Insert/Update pricing range config
      operationId: insertOrUpdatePricingRangeConfig
      requestBody:
        description: A JSON of config object to insert/update pricing range config
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PricingRangeConfigRequest'
      responses:
        '200':
          description: Response pricing range updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PricingRangeConfigsResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                required:
                  - error
                  - traceId
                  - path
                  - timestamp
                type: object
                properties:
                  timestamp:
                    type: string
                  traceId:
                    type: string
                  path:
                    type: string
                  error:
                    required:
                      - code
                      - message
                    type: object
                    properties:
                      code:
                        type: string
                      message:
                        type: string
                example: {
                  "traceId": "00000000000000000000000000000000",
                  "error": {
                    "message": "Internal Service Error",
                    "code": "InternalServiceError"
                  },
                  "path": "/v1.0/pricing/parameter-config",
                  "timestamp": "2023-07-13T04:05:34.310625200Z"
                }

  ############################ Dynamic Pricing Controller ############################
  ## Calculate multi fare for the trip
  /v1.0/pricing/multi-fare:
    post:
      tags:
        - Dynamic Pricing Controller
      summary: Calculate multi fare for the trip
      description: Calculate multi fare for the trip
      operationId: getMultiFare
      parameters:
        - in: header
          name: Traceparent
          schema:
            type: string
          required: true
      requestBody:
        description: Data need to be submitted to get multi fare
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetEstimatedFareInboundRequest'
        required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetEstimatedFareResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundResponse'
        '500':
          description: Interal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  ## Get multi-fare by fareId
  /v1.0/pricing/multi-fare/{fareId}:
    get:
      tags:
        - Dynamic Pricing Controller
      summary: Get multi-fare by fareId
      operationId: getMultiFareById
      parameters:
        - in: path
          name: fareId
          schema:
            type: string
          required: true
          description: "Multi-fare ID"
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetEstimatedFareResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  ## Insert fare breakdown to database
  /v1.0/pricing/store-fare-breakdown:
    post:
      tags:
        - Dynamic Pricing Controller
      summary: Insert fare breakdown to database
      description: Insert fare breakdown to database
      operationId: storeFareBreakdown
      parameters:
        - in: header
          name: Traceparent
          schema:
            type: string
          required: true
      requestBody:
        description: Data need to be submited to store fare breakdown
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StoreFareBreakdownInboundRequest'
        required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StoreFareBreakdownInboundResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  ## Check if the fare is valid
  /v1.0/pricing/verify-fare:
    post:
      tags:
        - Dynamic Pricing Controller
      summary: Check if the fare is valid
      operationId: verifyFare
      requestBody:
        description: Request need to be submited to verify the fare
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateFareRequest'
        required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateFareResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '500':
          description: Interal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  ## Get generated route by tripId
  /v1.0/pricing/generated-route/{tripId}:
    get:
      tags:
        - Dynamic Pricing Controller
      summary: Get generated route by tripId
      description: Get generated route by tripId
      operationId: getGeneratedRoute
      parameters:
        - in: path
          name: tripId
          schema:
            type: string
          required: true
          description: "Trip Id"
          example: trip-id
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetGeneratedRouteResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  ## Update the demand surges - Vesion 1
  /v1.0/pricing/update-dynamic-surge:
    post:
      tags:
        - Dynamic Pricing Controller
      summary: Update the demand surges
      operationId: updateDynamicSurge
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateDemandSuccessResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  ## Get additional charge fees by fareId, vehTypeId and productTypeId
  /v1.0/pricing/additional-charge-fees:
    get:
      tags:
        - Dynamic Pricing Controller
      summary: Get list of additional charge fee by fareId,vehTypeId and productTypeId
      description: get list of additional charge fee by fareId,vehTypeId and productTypeId.The data get from an exist cache,cacke key is "DYNAMIC_PRICING:MULTI_FARE:fareId".
      operationId: getAdditionalChargeFeesByCondition
      parameters:
        - in: query
          name: fareId
          schema:
            type: string
          description: Fare Id
          required: true
        - in: query
          name: vehTypeId
          schema:
            type: integer
          description: vehicle type id
          required: true
        - in: query
          name: productTypeId
          schema:
            type: string
          description: product type id ,also known as pdtId
          required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdditionalChargeFeesResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundResponse'
  ## Update the demand surges - Version 2
  /v2.0/pricing/update-dynamic-surge:
    post:
      tags:
        - Dynamic Pricing Controller
      summary: Update the demand surges
      operationId: updateDynamicSurgeV2
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateDemandSuccessResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  ## Get fare breakdown by fareId, bookingId, tripId
  /v1.0/pricing/fare-breakdown/search:
    post:
      tags:
        - Dynamic Pricing Controller
      summary: Get fare breakdown by fareId, bookingId, tripId
      description: Get fare breakdown by fareId, bookingId, tripId
      operationId: searchFareBreakdown
      requestBody:
        description: Data need to be submited to search fare breakdown
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchFareBreakdownInboundRequest'
        required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchFareBreakdownInboundResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Config Management
  ## Reload dynamic pricing configs to cache
  /v1.0/pricing/config/reload:
    post:
      tags:
        - Config management
      summary: Reload dynamic pricing configs to cache
      operationId: reloadConfigs
      parameters:
        - in: query
          name: configType
          description: Config type
          required: true
          schema:
            type: string
            enum: [ ALL, FLAT_FARE, FARE_TYPE, COMPANY_HOLIDAY, LOCATION_SURCHARGE, DYNP_SURGE ]
            example: "ALL"
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoadConfigsSuccessResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  ## Get/Create/Update New Pricing Mocdel Config
  /v2.0/pricing/config/new-pricing-model:
    get:
      tags:
        - Config Management
      summary: Get new pricing model config
      operationId: getNewPricingModelConfig
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NewPricingModelConfigListResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - Config Management
      summary: Create new pricing model config
      operationId: createNewPricingModelConfig
      requestBody:
        description: Request Create New Pricing Model Field
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NewPricingModelRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NewPricingModelConfigResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      tags:
        - Config Management
      summary: Update new pricing model config
      operationId: updateNewPricingModelConfig
      requestBody:
        description: Request updated New Pricing Model Field by id in properties
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NewPricingModelRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NewPricingModelConfigResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  ## Get/Add/Delete List of Excluded Booking Channel
  /v2.0/pricing/config/exclude-booking-channel:
    get:
      tags:
        - Config Management
      summary: Get list of exclude booking channel
      operationId: getExcludeBookingChannel
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExcludeBookingChannelListResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - Config Management
      summary: Add exclude booking channel
      operationId: addExcludeBookingChannel
      requestBody:
        description: Request for add an exclude booking channel
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExcludeBookingChannelRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExcludeBookingChannelResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      tags:
        - Config Management
      summary: Delete a booking channel from exclude list
      operationId: deleteExcludeBookingChannel
      requestBody:
        description: Delete a booking channel from exclude list
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExcludeBookingChannelRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExcludeBookingChannelResponse'
        '404':
          description: Not found booking channel to delete
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  ## Get/Add/Delete Job Status
  /v2.0/pricing/config/job-status:
    get:
      tags:
        - Config Management
      summary: Get list of job status
      operationId: getJobStatus
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobStatusListResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - Config Management
      summary: Add job status for fleet analytics
      operationId: addJobStatus
      requestBody:
        description: Request for adding job status for fleet analytics
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JobStatusRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobStatusResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      tags:
        - Config Management
      summary: Delete a job status
      operationId: deleteJobStatus
      requestBody:
        description: Request for add delete job status
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JobStatusRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobStatusResponse'
        '404':
          description: Not found job status to delete
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  ############################ H3 Region Surge Computation Management ############################
  ############################ Surge Computation Model Management ############################
  ## Get all surge computation models
  /v1.0/surge-computation/models:
    get:
      tags:
        - Surge Computation Model Management
      summary: Get all surge computation models
      operationId: getAllSurgeComputationModels
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SurgeComputationModelListResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - Surge Computation Model Management
      summary: Create a new surge computation model
      operationId: createSurgeComputationModel
      parameters:
        - name: X-User-Id
          in: header
          required: true
          schema:
            type: string
          description: User ID for auditing purposes
      requestBody:
        description: Surge computation model to create
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SurgeComputationModelRequest'
      responses:
        '200':
          description: Successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SurgeComputationModelResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  ## Get, Update, Delete a specific surge computation model
  /v1.0/surge-computation/models/{id}:
    get:
      tags:
        - Surge Computation Model Management
      summary: Get a surge computation model by ID
      operationId: getSurgeComputationModelById
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SurgeComputationModelResponse'
        '404':
          description: Surge computation model not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      tags:
        - Surge Computation Model Management
      summary: Update a surge computation model
      operationId: updateSurgeComputationModel
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
        - name: X-User-Id
          in: header
          required: true
          schema:
            type: string
          description: User ID for auditing purposes
      requestBody:
        description: Updated surge computation model
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SurgeComputationModelRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SurgeComputationModelResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestErrorResponse'
        '404':
          description: Surge computation model not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      tags:
        - Surge Computation Model Management
      summary: Delete a surge computation model, will also delete all related region based configurations
      operationId: deleteSurgeComputationModel
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
        - name: X-User-Id
          in: header
          required: true
          schema:
            type: string
          description: User ID for auditing purposes
      responses:
        '204':
          description: Successfully deleted
        '404':
          description: Surge computation model not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  ######################## Surge Computation Static Time-Based Configuration Management ########################
  /v1.0/surge-computation/static-time-based-configurations/effective-check:
    get:
      tags:
        - Surge Computation Static Time Based Configuration Management
      summary: Check if the current configuration out of date or close to expiration
      operationId: staticTimeBasedConfigurationEffectiveCheck
      responses:
        '200':
          description: If the current configuration out of date or close to expiration
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaticBasedConfigurationEffectiveCheckResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v1.0/surge-computation/static-time-based-configurations/versions:
    get:
      tags:
        - Surge Computation Static Time Based Configuration Management
      summary: Get all versions of surge computation time-based static configurations
      operationId: getStaticTimeBasedConfigurationVersions
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaticBasedConfigurationVersionListResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  ## Get all surge computation time-based static configurations
  /v1.0/surge-computation/static-time-based-configurations:
    get:
      tags:
        - Surge Computation Static Time Based Configuration Management
      summary: Get all surge computation time-based static configurations by version
      operationId: getStaticTimeBasedConfigurations
      parameters:
        - name: version
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaticTimeBasedConfigurationListResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - Surge Computation Static Time Based Configuration Management
      summary: Create a new surge computation time-based static configuration
      operationId: batchCreateStaticTimeBasedConfiguration
      parameters:
        - name: X-User-Id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              minItems: 1
              items:
                $ref: '#/components/schemas/StaticTimeBasedConfigurationRequest'
      responses:
        '200':
          description: Successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaticTimeBasedConfigurationCreateResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  ## Get, Update, Delete a specific surge computation time-based static configuration
  /v1.0/surge-computation/static-time-based-configurations/{id}:
    get:
      tags:
        - Surge Computation Static Time Based Configuration Management
      summary: Get a surge computation time-based static configuration by ID
      operationId: getStaticTimeBasedConfigurationById
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaticTimeBasedConfigurationResponse'
        '404':
          description: Configuration not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags:
        - Surge Computation Static Time Based Configuration Management
      summary: Update a surge computation time-based static configuration
      operationId: updateStaticTimeBasedConfiguration
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
        - name: X-User-Id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StaticTimeBasedConfigurationRequest'
      responses:
        '200':
          description: Successfully updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaticTimeBasedConfigurationResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Configuration not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      tags:
        - Surge Computation Static Time Based Configuration Management
      summary: Delete a surge computation time-based static configuration
      operationId: deleteStaticTimeBasedConfiguration
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
        - name: X-User-Id
          in: header
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Successfully deleted
        '404':
          description: Configuration not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  ####################### Surge Computation Static Region-Based Configuration Management ########################
  /v1.0/surge-computation/static-region-based-configurations/effective-check:
    get:
      tags:
        - Surge Computation Static Region Based Configuration Management
      summary: Check if the current configuration out of date or close to expiration
      operationId: staticRegionBasedConfigurationEffectiveCheck
      parameters:
        - in: query
          name: modelId
          schema:
            type: integer
            format: int64
          required: true
      responses:
        '200':
          description: If the current configuration out of date or close to expiration
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaticBasedConfigurationEffectiveCheckResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v1.0/surge-computation/{modelId}/static-region-based-configurations/versions:
    get:
      tags:
        - Surge Computation Static Region Based Configuration Management
      summary: Get all versions of surge computation region-based static configurations
      operationId: getStaticRegionBasedConfigurationVersions
      parameters:
        - name: modelId
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: "ID of the surge computation model to get the versions for"
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaticBasedConfigurationVersionListResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  ## Get all surge computation region-based-configurations
  /v1.0/surge-computation/{modelId}/static-region-based-configurations:
    get:
      tags:
        - Surge Computation Static Region Based Configuration Management
      summary: Get all surge computation static region-based configurations by modelId and version
      operationId: getStaticRegionBasedConfigurations
      parameters:
        - name: modelId
          in: path
          required: true
          schema:
            type: integer
            format: int64
          description: "ID of the surge computation model to get the configurations for"
        - name: version
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaticRegionBasedConfigurationListResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags:
        - Surge Computation Static Region Based Configuration Management
      summary: Batch update or create static region-based configuration, will determine if it exists based on name and effectiveFrom
      operationId: updateStaticRegionBasedConfiguration
      parameters:
        - name: modelId
          in: path
          required: true
          schema:
            type: integer
            format: int64
        - name: X-User-Id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              minItems: 1
              items:
                $ref: '#/components/schemas/StaticRegionBasedConfigurationRequest'
      responses:
        '204':
          description: Successfully updated
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Configuration not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /v1.0/surge-computation/static-region-based-configurations:
    post:
      tags:
        - Surge Computation Static Region Based Configuration Management
      summary: Batch create new static region-based configuration
      operationId: batchCreateStaticRegionBasedConfigurations
      parameters:
        - name: X-User-Id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              minItems: 1
              items:
                $ref: '#/components/schemas/StaticRegionBasedConfigurationRequest'
      responses:
        '200':
          description: Successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaticRegionBasedConfigurationBatchCreateResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  ## Get a specific static region-based configuration
  /v1.0/surge-computation/static-region-based-configurations/{id}:
    get:
      tags:
        - Surge Computation Static Region Based Configuration Management
      summary: Get a static region-based configuration by ID
      operationId: getStaticRegionBasedConfigurationById
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaticRegionBasedConfigurationResponse'
        '404':
          description: Configuration not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'


  ############################ Surge Computation Standard inputs ############################
  ## Get all standard inputs
  /v1.0/surge-computation/standard-inputs:
    get:
      tags:
        - Surge Computation Standard inputs
      summary: Retrieve all supported Live Standard Inputs
      operationId: getStandardInputs
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetStandardInputsResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  ############################ Surge Computation distribution ############################
  ## Get all region model distributions
  /v1.0/surge-computation/region-model-distribution:
    put:
      tags:
        - Surge Computation Region Model Distribution Management
      summary: Create or Update a region model distribution
      operationId: createOrUpdateRegionModelDistribution
      parameters:
        - name: X-User-Id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegionModelDistribution'
      responses:
        '204':
          description: Successful operation
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v1.0/surge-computation/region-model-distribution/region/{regionId}:
    get:
      tags:
        - Surge Computation Region Model Distribution Management
      summary: Get a region model distribution by region id
      operationId: getRegionModelDistribution
      parameters:
        - name: regionId
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRegionModelDistributionResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v1.0/surge-computation/region-model-distribution/id/{id}:
    delete:
      tags:
        - Surge Computation Region Model Distribution Management
      summary: Delete a region model distribution by id
      operationId: deleteRegionModelDistribution
      parameters:
        - name: X-User-Id
          in: header
          required: true
          schema:
            type: string
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '204':
          description: Successful operation
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  ############################ Surge Factor Calculation ############################
  ## Trigger surge factor calculation
  /v1.0/surge-computation/calculate-surge-factor:
    post:
      tags:
        - Surge Computation Management
      summary: Trigger calculation of surge factor
      description: Initiates the process to calculate surge factors across all regions
      operationId: calculateSurgeFactor
      responses:
        '204':
          description: Successfully initiated surge factor calculation
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  ############################ Get fare count for machine learning ############################
  ## Trigger to aggregate get fare count every minute
  /v1.0/machine-learning/aggregate-get-fare-count:
    post:
      tags:
        - Machine Learning Data Aggregation
      summary: Trigger to aggregate get fare count every minute
      description: Trigger to aggregate get fare count every minute
      operationId: aggregateGetFareCountEveryMinute
      responses:
        '204':
          description: Successfully initiated surge factor calculation
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'



components:
  schemas:
    SurgeComputationModelRequest:
      type: object
      required:
        - modelName
        - description
        - endpointUrl
      properties:
        modelName:
          type: string
          example: "Peak Hour Model"
        description:
          type: string
          example: "Model for computing surge during peak hours"
        endpointUrl:
          type: string
          example: "http://example.com/surge-compute"
        requestFieldsMappings:
          type: array
          description: "List of mappings between request parameters and configuration names"
          items:
            type: object
            required:
              - mappingType
              - requestParameterName
              - mappingConfigurationName
            properties:
              mappingType:
                type: string
                enum: [STATIC_TIME_BASED_CONFIGURATION, STATIC_REGION_BASED_CONFIGURATION, LIVE_STANDARD_INPUT]
                example: "STATIC_TIME_BASED_CONFIGURATION"
                description: "Type of configuration that the request parameter is mapped to"
              requestParameterName:
                type: string
                example: "dayOfWeek"
                description: "Name of the request parameter"
              mappingConfigurationName:
                type: string
                example: "peak_hour_surge"
                description: "Name of the configuration that the request parameter is mapped to"

    SurgeComputationModelResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/SurgeComputationModel'
        timestamp:
          type: string
          format: date-time
        traceId:
          type: string

    SurgeComputationModelListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/SurgeComputationModel'
        timestamp:
          type: string
          format: date-time
        traceId:
          type: string

    SurgeComputationModel:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
          description: "Primary key for the surge computation model"
        modelName:
          type: string
          example: "H3 Region Surge Model"
          description: "Name of the surge computation model"
        description:
          type: string
          example: "Model for computing surge based on H3 regions"
          description: "Description of the surge computation model"
        endpointUrl:
          type: string
          example: "http://example.com/api/surge-computation"
          description: "URL endpoint for the surge computation model"
        requestFieldsMappings:
          type: array
          description: "List of mappings between request parameters and configuration names"
          items:
            type: object
            properties:
              mappingType:
                type: string
                enum: [STATIC_TIME_BASED_CONFIGURATION, STATIC_REGION_BASED_CONFIGURATION, LIVE_STANDARD_INPUT]
                example: "STATIC_TIME_BASED_CONFIGURATION"
                description: "Type of configuration that the request parameter is mapped to"
              requestParameterName:
                type: string
                example: "dayOfWeek"
                description: "Name of the request parameter"
              mappingConfigurationName:
                type: string
                example: "peak_hour_surge"
                description: "Name of the configuration that the request parameter is mapped to"
        createdBy:
          type: string
          example: "admin"
          description: "User who created the record"
        createdDate:
          type: string
          format: date-time
          example: "2023-09-25T09:12:33.001Z"
          description: "Date and time when the record was created"
        updatedBy:
          type: string
          example: "admin"
          description: "User who last updated the record"
        updatedDate:
          type: string
          format: date-time
          example: "2023-09-25T09:12:33.001Z"
          description: "Date and time when the record was last updated"

    BookARideResponse:
      type: object
      properties:
        data:
          type: string
          example: "Init scheduler task successfully"
        timestamp:
          type: string
          example: 1681116843
        traceId:
          type: string
          example: 6433c664f53c933e668c442ddd11ad2e
    UpdateSuccessResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/FareTypeConfigResponseData"
    FareTypeConfigResponseData:
      type: object
      properties:
        fareTypeId:
          type: integer
          example: 2202
        fareType:
          type: string
          example: DYNP_MIN_CAP
        defaultFixed:
          type: number
          example: 0.02
        defaultPercent:
          type: number
          example: 0
        startDate:
          type: string
          example: "2023-08-29"
        endDate:
          type: string
          example: "2023-09-29"
        day:
          type: string
          example: MON
        hour:
          type: string
          example: 10
        createdDate:
          type: string
          example: "2023-08-29T09:12:33.001Z"
        createdBy:
          type: string
          example: user1
        updatedDate:
          type: string
          example: "2023-08-29T09:12:33.001Z"
        updatedBy:
          type: string
          example: user1

    FareTypeConfigsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/FareTypeConfig'
    FareTypeConfigRequest:
      type: object
      properties:
        fareType:
          type: string
          example: DYNP_MIN_CAP
        userChange:
          type: string
          example: user1
        configRequest:
          $ref: '#/components/schemas/ConfigRequest'
    FareTypeConfig:
      type: object
      properties:
        fareTypeId:
          type: integer
          example: 2202
        fareType:
          type: string
          example: DYNP_MIN_CAP
        defaultFixed:
          type: number
          example: 0.02
        defaultPercent:
          type: number
          example: 0
        startDate:
          type: string
          example: "2023-09-25"
        endDate:
          type: string
          example: "2023-09-29"
        createdDate:
          type: string
          example: "2023-08-29T09:12:33.001Z"
        createdBy:
          type: string
          example: user1
        updatedDate:
          type: string
          example: "2023-08-29T09:12:33.001Z"
        updatedBy:
          type: string
          example: user1
        day:
          type: string
          example: MON
        hour:
          type: string
          example: 10
    ConfigRequest:
      type: object
      properties:
        value:
          type: number
          example: 7.5
        day:
          type: string
          example: MON
        hour:
          type: string
          example: 10
        effectiveDate:
          type: string
          example: "2018-08-09T16:30:00+07:00"
        effectiveDateTo:
          type: string
          example: "2018-08-09T16:30:00+07:00"
        defaultPercent:
          type: number
          example: 0
    PricingRangeConfigsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/PricingRangeConfig'
    PricingRangeConfig:
      type: object
      properties:
        pricingRangeId:
          type: number
          example: 2202
        startPrice:
          type: number
          example: 0.01
        endPrice:
          type: number
          example: 0.02
        step:
          type: number
          example: 1
        refreshPeriod:
          type: number
          example: 10
        quoteValidPeriod:
          type: number
          example: 10
        day:
          type: string
          example: MON
        hour:
          type: string
          example: 10
        stepPositive:
          type: number
          example: 0
        stepNegative:
          type: number
          example: 0.02
        createdDate:
          type: string
          example: "2023-08-29T09:12:33.001Z"
        createdBy:
          type: string
          example: "ADMIN"
        updatedDate:
          type: string
          example: "2023-08-29T09:12:33.001Z"
        updatedBy:
          type: string
          example: "ADMIN"

    PricingRangeConfigRequest:
      type: object
      properties:
        user:
          type: string
          example: "ADMIN"
        isEnabled:
          type: number
          example: 1
        startPrice:
          type: number
          example: 0
        endPrice:
          type: number
          example: 0.12
        step:
          type: number
          example: 0.012
        refreshPeriod:
          type: number
          example: 60
        quoteValidPeriod:
          type: number
          example: 2
        day:
          type: string
          example: "ALL"
        hour:
          type: string
          example: "ALL"
        stepPositive:
          type: number
          example: 0
        stepNegative:
          type: number
          example: 0

    ValidateFareRequest:
      required:
        - fareId
        - countryCode
        - mobile
        - pickupAddressRef
        - dropoffAddressRef
        - vehTypeId
        - fareAmount
      type: object
      properties:
        fareId:
          type: string
          example: "8dda8a5f-2fea-4152-9b8c-d29eb32a50cd-1697686802785-36808288"
        countryCode:
          type: string
          example: "65"
        mobile:
          type: string
          example: "36808288"
        pickupAddressRef:
          type: string
          example: "154505"
        dropoffAddressRef:
          type: string
          example: "1001"
        intermediateAddrRef:
          type: string
          example: "1001"
        vehTypeId:
          type: number
          example: 100
        fareAmount:
          type: number
          example: 21.5

    ValidateFareResponse:
        type: object
        properties:
          data:
            type: object
            properties:
              isValidFare:
                type: boolean
                example: true

    LoadConfigsSuccessResponse:
      type: object
      properties:
        data:
          type: string
          example: Loaded configs successfully

    UpdateDemandSuccessResponse:
      type: object
      properties:
        data:
          type: string
          example: Update demand supply surge successfully


    AdditionalChargeFeesResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/AdditionalChargeFee'
    AdditionalChargeFee:
      type: object
      properties:
        chargeId:
          type: integer
          example: 1
        chargeType:
          type: string
          example: "DRIVER_FEE"
        chargeAmt:
          type: number
          example: 0.50
        chargeThreshold:
          type: number
          example: 16.00
        chargeUpperLimit:
          type: number
          example: 0.50
        chargeLowerLimit:
          type: number
          example: 0.30

    AdditionalChargeConfig:
      type: object
      properties:
        chargeId:
          type: integer
          example: 1
        chargeType:
          type: string
          example: "DRIVER_FEE"
        chargeThreshold:
          type: number
          example: 16.00
        chargeUpperLimit:
          type: number
          example: 0.50
        chargeLowerLimit:
          type: number
          example: 0.30

    ErrorResponse:
      required:
        - error
        - traceId
        - path
        - timestamp
      type: object
      properties:
        traceId:
          type: string
          example: "1234567"
        error:
          $ref: '#/components/schemas/Error'
        timestamp:
          type: string
          example: 2000-08-29T09:12:33.001Z
        path:
          type: string
          example: </sample-api-path>

    BadRequestErrorResponse:
      required:
        - error
        - traceId
        - path
        - timestamp
      type: object
      properties:
        traceId:
          type: string
          example: "1234567"
        error:
          $ref: '#/components/schemas/BadRequestError'
        timestamp:
          type: string
          example: 2000-08-29T09:12:33.001Z
        path:
          type: string
          example: </sample-api-path>

    BadRequestError:
      required:
        - code
        - message
        - data
      type: object
      properties:
        code:
          type: string
          example: BadRequest
        message:
          type: string
          example: Invalid request content
        data:
          $ref: '#/components/schemas/ErrorData'

    NotFoundResponse:
      required:
        - error
        - path
        - timestamp
      type: object
      properties:
        timestamp:
          type: string
          example: 2000-08-29T09:12:33.001Z
        traceId:
          type: string
          example: 1234567498
        path:
          type: string
          example: /request/to/path
        error:
          allOf:
            - $ref: '#/components/schemas/ErrorNotFound'
            - type: object

    ErrorNotFound:
      required:
        - code
        - message
        - data
      type: object
      properties:
        code:
          type: string
          example: EntityNotFoundException
        message:
          type: string
          example: Can not found any data

    GetEstimatedFareInboundRequest:
      required:
        - countryCode
        - mobile
        - jobType
        - bookingChannel
        - pickupAddressRef
        - pickupAddressLat
        - pickupAddressLng
        - pickupZoneId
        - destAddressRef
        - destAddressLat
        - destAddressLng
        - destZoneId
        - vehTypeIDs
      type: object
      properties:
        countryCode:
          type: string
          example: "65"
        bookingChannel:
          type: string
          enum: [IPHONE, ANDROID, CSA, SMS, IVR, IRD, IRD_POSTAL, OPENAPI, MWF, H5ALIPAY, H5, H5DBSPAYLAH, H5LAZADA, H5KRISPLUS]
          example: "ANDROID"
        mobile:
          type: string
          example: "91234567"
        jobType:
          type: string
          enum: [IMMEDIATE, ADVANCE, STREET]
          example: "IMMEDIATE"
        pickupAddressRef:
          type: string
          example: "1001"
        pickupAddressLat:
          type: number
          format: double
          example: 1.3672514
        pickupAddressLng:
          type: number
          format: double
          example: 103.9040206
        pickupZoneId:
          type: string
          example: "95"
        destAddressRef:
          type: string
          example: "1001"
        destAddressLat:
          type: number
          format: double
          example: 1.3579843
        destAddressLng:
          type: number
          format: double
          example: 103.7890947
        destZoneId:
          type: string
          example: "95"
        intermediateAddrRef:
          type: string
          example: "1001"
        intermediateAddrLat:
          type: number
          format: double
          example: 1.3579843
        intermediateAddrLng:
          type: number
          format: double
          example: 103.7890947
        intermediateZoneId:
          type: string
          example: "95"
        vehTypeIDs:
          type: array
          items:
            type: integer
            example: 0
        fareDate:
          type: string
          format: date-time
          example: "2025-01-27T19:20:11.234Z"

    GetEstimatedFareResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/GetEstimatedFareInboundResponse'

    GetPickupLocationSurchargeRequest:
      type: object
      required:
        - chargeBy
        - addressRef
        - productId
        - requestDate
      properties:
        chargeBy:
          type: string
          enum: [ PICKUP, DEST ]
          example: "PICKUP"
        addressRef:
          type: string
          example: "154505"
          description: The addressRef
        productId:
          type: string
          enum: [ STD001, FLAT-001, GUD-001, GUA-001, OWT-001, FLAT-002 ]
          description: The product ID
          example: "STD001"
        requestDate:
          type: string
          format: date-time
          example: "2023-11-16T15:03:17.287881Z"

    GetPickupLocationSurchargeResponse:
      type: object
      properties:
        fareType:
          type: string
          example: "LOC_SURC"
        applicableDays:
          type: string
          example: "HOL"
        startTime:
          type: string
          example: "17:00:00"
        endTime:
          type: string
          example: "23:59:59"
        chargeBy:
          type: string
          example: "PICKUP"
        surchargeValue:
          type: number
          example: 3.0
        locationId:
          type: string
          example: "104"
        locationName:
          type: string
          example: "Ulu Pandan Bus Depot"
        addressRef:
          type: string
          example: "106920"
        productId:
          type: string
          example: "STD001"
        dayIndicator:
          type: string
          example: "HOL"

    UpdateCBDAddressRequest:
      type: object
      required:
        - cbdAddressList
      properties:
        cbdAddressList:
          type: array
          items:
            $ref: '#/components/schemas/CBDAddressRequest'
    CBDAddressRequest:
      type: object
      required:
        - addressRef
        - cbdFlag
        - effectiveFlag
      properties:
        addressRef:
          type: string
          example: "217667"
        cbdFlag:
          type: boolean
          example: true
        effectiveFlag:
          type: boolean
          example: true
    LocReloadCacheRequest:
      type: object
      required:
        - addressRefList
      properties:
        addressRefList:
          type: array
          items:
            type: string
            example: 279555

    GetEstimatedFareInboundResponse:
      type: object
      properties:
        fareId:
          type: string
          example: "randomUUID + fareCalTime + mobileId"
        fareCalcTime:
          type: string
          format: date-time
        estimatedTripTime:
          type: integer
          format: int64
          description: estimated Trip Time in seconds
          example: 1050
        distance:
          type: integer
          format: int64
          description: estimated distance in meter
          example: 120
        encodedPolyline:
          type: string
          description: polyline of route
          example: "w|cGkicyRA~@EJUPQNiAcA[YGGSSrByB@KVYUSuBxBg@f@o@n@G|@?R?f@@RdBbB~@dA^f@l@|@~@xALRJP^p@LV~@bBNBV`@b@z@j@hA\\z@P`@tArD@"
        tripId:
          type: string
          description: tripId to report bilable trip
          example: "randomUUID"
        fareExpiredIn:
          type: integer
          example: 8
          description: "Fare expired in minute"
        countryCode:
          type: string
          example: "65"
          description: Country code of mobile
        mobile:
          type: string
          example: "********"
          description: mobile number
        pickupAddressRef:
          type: string
          example: "154505"
          description: pickup address reference
        destAddressRef:
          type: string
          example: "1001"
          description: destination address reference
        intermediateAddrRef:
          type: string
          example: "1001"
          description: intermediate address reference
        flatFareVOParts:
          type: array
          items:
            $ref: '#/components/schemas/FlatfareVOPart'

    StaticBasedConfigurationEffectiveCheckResponse:
      type: object
      properties:
        isWarning:
          type: boolean
          example: true
        isEffective:
          type: boolean
          example: true
        version:
          type: string
          example: "1.0"
          description: "Version of the configuration"
        effectiveFrom:
          type: string
          format: date-time
          example: "2023-09-25T00:00:00Z"
          description: Date and time from which the configuration is effective
        effectiveTo:
          type: string
          format: date-time
          example: "2024-09-25T00:00:00Z"

    StaticBasedConfigurationVersionListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/StaticBasedConfigurationVersion'
        timestamp:
          type: string
          format: date-time
          example: "2023-09-25T09:12:33.001Z"
        traceId:
          type: string
    StaticBasedConfigurationVersion:
      type: object
      properties:
        version:
          type: string
          example: "1.0"
          description: "Version of the configuration"
        isInUse:
          type: boolean
          example: true
          description: Whether this version is in used version.
        effectiveFrom:
          type: string
          format: date-time
          example: "2023-09-25T00:00:00Z"
          description: Date and time from which the configuration is effective
        effectiveTo:
          type: string
          format: date-time
          example: "2024-09-25T00:00:00Z"

    StaticTimeBasedConfigurationRequest:
      type: object
      required:
        - name
        - version
        - effectiveFrom
        - appliedHours
      properties:
        name:
          type: string
          example: "Peak Hour Surge"
          description: "Name of the time-based static configuration"
        version:
          type: string
          example: "1.0"
          description: "Version of the configuration"
        effectiveFrom:
          type: string
          format: date-time
          example: "2023-09-25T00:00:00Z"
          description: "Date and time from which the configuration is effective"
        effectiveTo:
          type: string
          format: date-time
          example: "2024-09-25T00:00:00Z"
          description: "Date and time until which the configuration is effective (null means indefinite)"
        description:
          type: string
          example: "Configuration for peak hour surge pricing"
          description: "Description of the configuration"
        appliedHours:
          type: array
          description: "List of applied hours with day of week, hour of day, and value"
          minItems: 1
          items:
            type: object
            required:
              - dayOfWeek
              - hourOfDay
              - value
            properties:
              dayOfWeek:
                type: string
                enum: [MON, TUE, WED, THU, FRI, SAT, SUN, PUBLIC_HOLIDAY]
                example: "MON"
                description: "Day of week (MON, TUE, WED, THU, FRI, SAT, SUN, PUBLIC_HOLIDAY)"
              hourOfDay:
                type: integer
                minimum: 0
                maximum: 23
                example: 8
                description: "Hour of day (0-23)"
              value:
                type: string
                example: "1.5"
                description: "Surge multiplier value"


    StaticTimeBasedConfiguration:
      allOf:
        - $ref: '#/components/schemas/StaticTimeBasedConfigurationRequest'
        - type: object
          properties:
            id:
              type: integer
              format: int64
              example: 1
              description: "Unique identifier"
            createdBy:
              type: string
              example: "john.doe"
              description: "User ID who created the configuration"
            createdDate:
              type: string
              format: date-time
              example: "2023-09-25T09:12:33.001Z"
              description: "Creation timestamp"
            updatedBy:
              type: string
              example: "jane.doe"
              description: "User ID who last updated the configuration"
            updatedDate:
              type: string
              format: date-time
              example: "2023-09-25T09:12:33.001Z"
              description: "Last update timestamp"

    StaticTimeBasedConfigurationCreateResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/StaticTimeBasedConfiguration'
        timestamp:
          type: string
          format: date-time
          example: "2023-09-25T09:12:33.001Z"
        traceId:
          example: "6433c664f53c933e668c442ddd11ad2e"

    StaticTimeBasedConfigurationResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/StaticTimeBasedConfiguration'
        timestamp:
          type: string
          format: date-time
          example: "2023-09-25T09:12:33.001Z"
        traceId:
          example: "6433c664f53c933e668c442ddd11ad2e"

    StaticTimeBasedConfigurationListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/StaticTimeBasedConfiguration'
        timestamp:
          type: string
          format: date-time
          example: "2023-09-25T09:12:33.001Z"
        traceId:
          type: string
          example: "6433c664f53c933e668c442ddd11ad2e"

    FlatfareVOPart:
      type: object
      properties:
        vehTypeId:
          type: integer
          example: 100
        totalFare:
          type: number
          description: for dynamic pricing
          example: 21.5
        estimatedFareLF:
          type: number
          description: for meter fare low
          example: 18.0
        estimatedFareRT:
          type: number
          description: for meter fare high
          example: 23.0
        pdtId:
          type: string
          example: "FLAT-001"
        drvSurgeLvl:
          type: integer
          example: 2
        paxSurgeIndicator:
          type: integer
          example: 1
        platformFeeConfigId:
          type: number
          description: ID of platform fee config
          example: 1
        platformFeeLower:
          type: number
          format: double
          description: for platform fee applied for lower price
          example: 23.0
        platformFeeUpper:
          type: number
          format: double
          description: for platform fee applied for higher price
          example: 23.0
        additionalCharges:
          type: array
          items:
            $ref: '#/components/schemas/AdditionalChargeConfig'

    StoreFareBreakdownInboundRequest:
      required:
        - fareId
        - vehicleTypeId
        - bookingId
      type: object
      properties:
        fareId:
          type: string
          example: "3435477b-ad74-4859-a966-eaa00dd03241-1699336533592-********"
        vehicleTypeId:
          type: integer
          example: 100
        bookingId:
          type: string
          example: "booking_id"

    StoreFareBreakdownInboundResponse:
      type: object
      properties:
        data:
          type: object
          properties:
            success:
              type: boolean
              example: true

    GetGeneratedRouteResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/GeneratedRoute'

    GeneratedRoute:
      type: object
      properties:
        pickupPoint:
          $ref: '#/components/schemas/LatLng'
        destinationPoint:
          $ref: '#/components/schemas/LatLng'
        intermediatePoint:
          $ref: '#/components/schemas/LatLng'
        distance:
          type: number
          description: estimated distance in meter
          example: 5.0
        duration:
          type: number
          example: 1200
        encodedPolyline:
          type: string
          description: polyline of route
          example: "w|cGkicyRA~@EJUPQNiAcA[YGGSSrByB@KVYUSuBxBg@f@o@n@G|@?R?f@@RdBbB~@dA^f@l@|@~@xALRJP^p@LV~@bBNBV`@b@z@j@hA\\z@P`@tArD@"

    LatLng:
      type: object
      properties:
        lat:
          type: number
          example: 1.324103125052384
        lng:
          type: number
          example: 103.94436932842119

    Error:
      required:
        - code
        - message
        - data
      type: object
      properties:
        code:
          type: string
          example: BadRequest
        message:
          type: string
          example: Multiple errors in user register request
        data:
          $ref: '#/components/schemas/ErrorData'

    ErrorData:
      required:
        - validation
        - message
      type: object
      properties:
        fields:
          type: array
          items:
            $ref: '#/components/schemas/Field'

    Field:
      required:
        - name
        - message
      type: object
      properties:
        name:
          type: string
          example: type
        message:
          type: array
          items:
            type: string
            example: invalid bound

    SearchFareBreakdownInboundRequest:
      type: object
      properties:
        fareId:
          type: string
          example: "3435477b-ad74-4859-a966-eaa00dd03241-1699336533592-********"
        bookingId:
          type: string
          example: "************"
        tripId:
          type: string
          example: "c84dd872-2e02-447c-8a15-6b02c55cc90e20231127094511"


    SearchFareBreakdownInboundResponse:
      type: object
      properties:
        fareId:
          type: string
          example: "2b8d7be6-0a6c-4292-a84f-2fda30fdf704-*************-********"
        bookingId:
          type: string
          example: "************"
        tripId:
          type: string
          example: "c84dd872-2e02-447c-8a15-6b02c55cc90e20231127094511"
        flagDownRate:
          type: number
          format: double
          example: 4.00
        waitTimeFare:
          type: number
          format: double
          example: 2.82
        routingDistance:
          type: integer
          example: 13000
        ett:
          type: integer
          example: 1200
        dpFinalFare:
          type: number
          format: double
          example: 26.812
        totalFare:
          type: number
          format: double
          example: 30.5
        estimatedFareLF:
          type: number
          format: double
          example: 29.5
        estimatedFareRT:
          type: number
          format: double
          example: 34
        flatPlatformFeeId:
          type: integer
          example: 1
        flatPlatformFee:
          type: number
          format: double
          example: 0.3
        meterPlatformFeeId:
          type: integer
          example: 2
        meterPlatformFeeLower:
          type: number
          format: double
          example: 0.3
        meterPlatformFeeUpper:
          type: number
          format: double
          example: 0.5
        additionalCharges:
          type: array
          items:
            $ref: '#/components/schemas/AdditionalChargeConfig'

    NewPricingModelConfigListResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/NewPricingModelConfigResponse'
    NewPricingModelRequest:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: The id of properties
        index:
          type: integer
          format: int64
        zoneId:
          type: string
          description: The zoneId that config will be applied
        startDt:
          type: string
          format: date-time
          description: The start date time this config is valid
        endDt:
          type: string
          format: date-time
          description: The end date time this config is valid
        additionalSurgeHigh:
          type: integer
          description: Aditional Surge High
        surgeHighTierRate:
          type: number
          format: double
          description: Surge High Tier Rate
        unmetRate1:
          type: number
          format: double
          description: Unmet Rate 1
        unmetRate2:
          type: number
          format: double
          description: Unmet Rate 2
        negativeDemandSupplyDownRate:
          type: number
          format: double
          description: Negative Demand Supply Down Rate
        k1:
          type: number
          format: double
          description: K1 modifier for V3
        k2:
          type: number
          format: double
          description: K2 modifier for V3
        k3:
          type: number
          format: double
          description: K3 modifier for V3
        k4:
          type: number
          format: double
          description: K4 modifier for V3
        k5:
          type: number
          format: double
          description: K5 modifier for V3
        k6:
          type: number
          format: double
          description: K6 modifier for V3
        k7:
          type: number
          format: double
          description: K7 modifer for K7
        k8:
          type: number
          format: double
          description: K8 modifier for K8
        zonePriceVersion:
          type: string
          description: The version of formula to calculate surge value
    NewPricingModelConfigResponse:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: The id of properties
        index:
          type: integer
          format: int64
        zoneId:
          type: string
          description: The zoneId that config will be applied
        startDt:
          type: string
          format: date-time
          description: The start date time this config is valid
        endDt:
          type: string
          format: date-time
          description: The end date time this config is valid
        additionalSurgeHigh:
          type: integer
          description: Aditional Surge High
        surgeHighTierRate:
          type: number
          format: double
          description: Surge High Tier Rate
        unmetRate1:
          type: number
          format: double
          description: Unmet Rate 1
        unmetRate2:
          type: number
          format: double
          description: Unmet Rate 2
        negativeDemandSupplyDownRate:
          type: number
          format: double
          description: Negative Demand Supply Down Rate
        k1:
          type: number
          format: double
          description: K1 modifier for V3
        k2:
          type: number
          format: double
          description: K2 modifier for V3
        k3:
          type: number
          format: double
          description: K3 modifier for V3
        k4:
          type: number
          format: double
          description: K4 modifier for V3
        k5:
          type: number
          format: double
          description: K5 modifier for V3
        k6:
          type: number
          format: double
          description: K6 modifier for V3
        k7:
          type: number
          format: double
          description: K7 modifer for K7
        k8:
          type: number
          format: double
          description: K8 modifier for K8
        zonePriceVersion:
          type: string
          description: The version of formula to calculate surge value
        createdBy:
          type: string
        createdDt:
          type: string
          format: date-time
        updatedBy:
          type: string
        updatedDt:
          type: string
          format: date-time
    ExcludeBookingChannelRequest:
      type: object
      properties:
        booking_channel:
          type: string
          example: "One of H5DBSPAYLAH,IRD,GOJEK,ANDROID,IPHONE,MOTORIST,SMS,CSA,H5KRISPLUS,IRD_POSTAL,MWF"

    ExcludeBookingChannelResponse:
      type: object
      properties:
        booking_channel:
          type: string
    ExcludeBookingChannelListResponse:
      type: object
      properties:
        items:
          type: array
          items:
            type: string
    JobStatusRequest:
      type: object
      properties:
        job_status:
          type: string
          example: "One of NEW,ARRIVAL,PENDING,CONFIRMED,ONBOARD,NO_SHOW,NO_SHOW_UR,COMPLETED,FAILED,NTA,CANCELLED,CANCELLED_UR,MODIFY,STC_COMPLETED"
    JobStatusResponse:
      type: object
      properties:
        job_status:
          type: string
    JobStatusListResponse:
      type: object
      properties:
        items:
          type: array
          items:
            type: string

    DynamicSurgeResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/DynamicSurgeData'
    DynamicSurgeData:
      type: object
      properties:
        zoneId:
          type: integer
          example: 1
        surge:
          type: integer
          example: 1
        surgeLow:
          type: integer
          example: 1
        surgeHigh:
          type: integer
          example: 1
        demandRecent:
          type: integer
          example: 1
        demandPrevious:
          type: integer
          example: 1
        demandPredicted:
          type: integer
          example: 1
        supply:
          type: integer
          example: 1
        excessDemand:
          type: integer
          example: 1
        lastUpdDt:
          type: string
          example: "2023-08-29T09:12:33.001Z"
        prevSurge:
          type: integer
          example: 1
        batchKey:
          type: integer
          example: 1
        zonePriceModel:
          type: string
          example: "DYNP_MIN_CAP"
        predictedDemand15:
          type: integer
          example: 1
        demand15:
          type: integer
          example: 1
        previousUnmet15:
          type: number
          example: 1
        unmet15:
          type: number
          example: 1
        excessDemand15:
          type: integer
          example: 1

    GetStandardInputsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/StandardInput'
    StandardInput:
      type: object
      properties:
        name:
          type: string
          example: "ComfortRideDemand"
        description:
          type: string
          example: "ComfortRide Demand by region"

    StaticRegionBasedConfigurationRequest:
      type: object
      required:
        - name
        - version
        - effectiveFrom
        - regionValues
      properties:
        name:
          type: string
          example: "Region Surge Multiplier"
          description: "Name of the region-based static configuration"
        version:
          type: string
          example: "1.0"
          description: "Version of the configuration"
        effectiveFrom:
          type: string
          format: date-time
          example: "2023-09-25T00:00:00Z"
          description: "Date and time from which the configuration is effective"
        effectiveTo:
          type: string
          format: date-time
          example: "2024-09-25T00:00:00Z"
          description: "Date and time until which the configuration is effective (null means indefinite)"
        description:
          type: string
          example: "Configuration for region-based surge pricing"
          description: "Description of the configuration"
        regionValues:
          type: array
          description: "List of region values with region ID and value"
          minItems: 1
          items:
            type: object
            required:
              - regionId
              - value
            properties:
              regionId:
                type: integer
                format: int64
                example: 1
                description: "Region ID"
              value:
                type: string
                example: "1.5"
                description: "Value for the region-based configuration"

    StaticRegionBasedConfiguration:
      allOf:
        - $ref: '#/components/schemas/StaticRegionBasedConfigurationRequest'
        - type: object
          properties:
            id:
              type: integer
              format: int64
              example: 1
              description: "Unique identifier"
            createdBy:
              type: string
              example: "john.doe"
              description: "User ID who created the configuration"
            createdDate:
              type: string
              format: date-time
              example: "2023-09-25T09:12:33.001Z"
              description: "Creation timestamp"
            updatedBy:
              type: string
              example: "jane.doe"
              description: "User ID who last updated the configuration"
            updatedDate:
              type: string
              format: date-time
              example: "2023-09-25T09:12:33.001Z"
              description: "Last update timestamp"

    StaticRegionBasedConfigurationBatchCreateResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/StaticRegionBasedConfiguration'
        timestamp:
          type: string
          format: date-time
          example: "2023-09-25T09:12:33.001Z"
        traceId:
          type: string
          example: "6433c664f53c933e668c442ddd11ad2e"
    StaticRegionBasedConfigurationResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/StaticRegionBasedConfiguration'
        timestamp:
          type: string
          format: date-time
          example: "2023-09-25T09:12:33.001Z"
        traceId:
          type: string
          example: "6433c664f53c933e668c442ddd11ad2e"

    StaticRegionBasedConfigurationListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/StaticRegionBasedConfiguration'
        timestamp:
          type: string
          format: date-time
          example: "2023-09-25T09:12:33.001Z"
        traceId:
          type: string
          example: "6433c664f53c933e668c442ddd11ad2e"

    GetAllRegionModelDistributionsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetRegionModelDistributionResponse'
    GetRegionModelDistributionResponse:
      type: object
      properties:
        regionId:
          type: integer
          format: int64
          example: 1
        modelDistributionVersions:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                format: int64
                example: 1
              isInUse:
                type: boolean
                example: true
                description: Whether this version is in used version.
              effectiveFrom:
                type: string
                format: date-time # Specify string format
                example: "2025-04-01T08:00:00.001Z" # Corrected example format
                description: The date and time (inclusive) from which this region definition is effective (ISO 8601 format).
              effectiveTo:
                type: string
                format: date-time # Specify string format
                example: "2026-03-31T23:59:59.999Z" # Corrected example format, made distinct from 'from'
                description: The date and time (exclusive or inclusive, specify) until which this region definition is effective (ISO 8601 format). Can be null/omitted for indefinite effectiveness.
              models:
                type: array
                items:
                  type: object
                  description: The model percentage for this region. Note, the sum of the percentage should must be 100.
                  properties:
                    modelId:
                      type: integer
                      format: int64
                      example: 1
                      description: The surge computation model id
                    modelName:
                      type: string
                      example: ModelA
                      description: The surge computation model name
                    percentage:
                      type: number
                      minimum: 0
                      maximum: 100
                      example: 80
                      description: The percentage of this model, the unit is "%"
    RegionModelDistribution:
      type: object
      required:
        - regionId
        - effectiveFrom
        - models
      properties:
        id:
          type: integer
          format: int64
          example: 1
          description: If null, create; if not null, update.
        regionId:
          type: integer
          format: int64
          example: 1
        effectiveFrom:
          type: string
          format: date-time # Specify string format
          example: "2025-04-01T08:00:00.001Z" # Corrected example format
          description: The date and time (inclusive) from which this region definition is effective (ISO 8601 format).
        effectiveTo:
          type: string
          format: date-time # Specify string format
          example: "2026-03-31T23:59:59.999Z" # Corrected example format, made distinct from 'from'
          description: The date and time (exclusive or inclusive, specify) until which this region definition is effective (ISO 8601 format). Can be null/omitted for indefinite effectiveness.
        models:
          type: array
          items:
            type: object
            description: The model percentage for this region. Note, the sum of the percentage should must be 100.
            required:
              - modelId
              - percentage
            properties:
              modelId:
                type: integer
                format: int64
                example: 1
                description: The surge computation model id
              percentage:
                type: number
                minimum: 0
                maximum: 100
                example: 80
                description: The percentage of this model, the unit is "%"