package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl.MlGetFareRequestAggStatsServiceImpl;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.MachineLearningDataAggregationApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for machine learning data aggregation operations. This controller implements the
 * MachineLearningDataAggregationApi interface.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class MachineLearningDataAggregationController implements MachineLearningDataAggregationApi {

  private final MlGetFareRequestAggStatsServiceImpl mlGetFareRequestAggStatsService;

  @Override
  public ResponseEntity<Void> aggregateGetFareCountEveryMinute() {
    mlGetFareRequestAggStatsService.aggregateGetFareCountEveryMinute();
    log.info("Get fare count aggregated successfully");
    return ResponseEntity.noContent().build();
  }
}
