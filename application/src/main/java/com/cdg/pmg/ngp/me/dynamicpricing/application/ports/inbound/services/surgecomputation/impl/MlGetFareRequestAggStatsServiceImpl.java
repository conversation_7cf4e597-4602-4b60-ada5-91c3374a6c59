package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.MlGetFareRequestAggStatsService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.farecounter.RequestCounterService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.MlGetFareRequestAggStatsRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RequestCountConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.FareCountAggregateResult;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.MlGetFareRequestAggStatsEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class MlGetFareRequestAggStatsServiceImpl implements MlGetFareRequestAggStatsService {
  
  private final RequestCounterService requestCounterService;
  private final MlGetFareRequestAggStatsRepository mlGetFareRequestAggStatsRepository;
  
  @Override
  public void aggregateGetFareCountEveryMinute() {

    // Get the previous minute as the aggregation interval
    final Instant now = Instant.now();
    final Instant endTime = now.truncatedTo(ChronoUnit.MINUTES);
    final Instant startTime = endTime.minus(1, ChronoUnit.MINUTES);
    List<FareCountAggregateResult> requestCountList = requestCounterService.getRequestCount(RequestCountConstant.MULTI_FARE, startTime, endTime);

    if (CollectionUtils.isEmpty(requestCountList)) {
      if (log.isDebugEnabled()) {
        log.debug("No request count found, endpoint: {}, startTime: {}, endTime: {}",
            RequestCountConstant.MULTI_FARE, startTime, endTime);
      }
      return;
    }
    
    final List<MlGetFareRequestAggStatsEntity> entities = requestCountList.stream()
        .map(v -> MlGetFareRequestAggStatsEntity.builder()
            .startTimestamp(startTime)
            .endTimestamp(endTime)
            .areaType(v.getAreaType().getValue())
            .pickupRegionId(v.getH3RegionId())
            .regionVersion(v.getRegionVersion())
            .modelId(v.getModelId())
            .getFareCount(v.getGetFareCount())
            .createTimestamp(Instant.now())
            .build())
        .toList();
    mlGetFareRequestAggStatsRepository.saveAll(entities);
  }
}
