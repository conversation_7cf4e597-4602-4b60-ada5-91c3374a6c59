package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.farecounter;

import com.cdg.pmg.ngp.me.dynamicpricing.constants.RequestCountConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.FareCountAggregateResult;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.GetFareCountEntity;
import java.time.Instant;
import java.util.List;
import java.util.Map;

public interface RequestCounterService {

  /**
   * Get request count for specified time window
   *
   * @param endpoint the endpoint need to get the request count. {@link RequestCountConstant}
   * @param startTime the start time
   * @param endTime the end tim
   * @return the list of {@link FareCountAggregateResult}
   * @see RequestCountConstant
   */
  List<FareCountAggregateResult> getRequestCount(
      String endpoint, Instant startTime, Instant endTime);

  /**
   * Get request count for specified time window
   * 
   * @param endpoint the endpoint need to get the request count. {@link RequestCountConstant}
   * @param startTime the start time
   * @param endTime the end tim
   * @return the request count in the specific time range
   * @see RequestCountConstant
   */
  Map<Long, Long> getRequestCountByRegions(String endpoint, Instant startTime, Instant endTime);

  /**
   * Record single request to local cache
   *
   * @param endpoint the endpoint need to record request count. {@link RequestCountConstant}
   * @param getFareCountEntity the {@link GetFareCountEntity}
   * @see RequestCountConstant
   */
  void recordEndpointRequest(String endpoint, GetFareCountEntity getFareCountEntity);
}
